// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
};

// ----------------------------------------------------------------------

export const paths = {
  faqs: '/faqs',
  minimalStore: 'https://mui.com/store/items/minimal-dashboard/',
  // AUTH
  auth: {
    amplify: {
      signIn: `${ROOTS.AUTH}/amplify/sign-in`,
      verify: `${ROOTS.AUTH}/amplify/verify`,
      signUp: `${ROOTS.AUTH}/amplify/sign-up`,
      updatePassword: `${ROOTS.AUTH}/amplify/update-password`,
      resetPassword: `${ROOTS.AUTH}/amplify/reset-password`,
    },
    jwt: {
      signIn: `${ROOTS.AUTH}/jwt/sign-in`,
      signUp: `${ROOTS.AUTH}/jwt/sign-up`,
      forgotPassword: `${ROOTS.AUTH}/jwt/forgot-password`,
      newPassword: `${ROOTS.AUTH}/jwt/new-password`,
    },
    firebase: {
      signIn: `${ROOTS.AUTH}/firebase/sign-in`,
      verify: `${ROOTS.AUTH}/firebase/verify`,
      signUp: `${ROOTS.AUTH}/firebase/sign-up`,
      resetPassword: `${ROOTS.AUTH}/firebase/reset-password`,
    },
    auth0: {
      signIn: `${ROOTS.AUTH}/auth0/sign-in`,
    },
    supabase: {
      signIn: `${ROOTS.AUTH}/supabase/sign-in`,
      verify: `${ROOTS.AUTH}/supabase/verify`,
      signUp: `${ROOTS.AUTH}/supabase/sign-up`,
      updatePassword: `${ROOTS.AUTH}/supabase/update-password`,
      resetPassword: `${ROOTS.AUTH}/supabase/reset-password`,
    },
  },
  // DASHBOARD
  dashboard: {
    root: ROOTS.DASHBOARD,
    categories: {
      root: `${ROOTS.DASHBOARD}/categories`,
      create: `${ROOTS.DASHBOARD}/categories/create`,
    },
    agents: {
      root: `${ROOTS.DASHBOARD}/agents`,
      create: `${ROOTS.DASHBOARD}/agents/create`,
      edit: `${ROOTS.DASHBOARD}/agents/edit/:id`,
    },
    teams: {
      root: `${ROOTS.DASHBOARD}/teams`,
    },
    managers: {
      root: `${ROOTS.DASHBOARD}/managers`,
      create: `${ROOTS.DASHBOARD}/managers/create`,
      edit: `${ROOTS.DASHBOARD}/managers/edit/:id`,
    },
    users: {
      root: `${ROOTS.DASHBOARD}/users`,
    },
    profile: {
      root: `${ROOTS.DASHBOARD}/profile`,
      knowledgeBase: `${ROOTS.DASHBOARD}/profile/knowledge-base`,
      preferences: `${ROOTS.DASHBOARD}/profile/preferences`,
      settings: `${ROOTS.DASHBOARD}/profile/settings`,
    },
    alharamain: {
      root: `${ROOTS.DASHBOARD}/alharamain`,
      agents: `${ROOTS.DASHBOARD}/alharamain/agents`,
    },
    group: {
      root: `${ROOTS.DASHBOARD}/group`,
      five: `${ROOTS.DASHBOARD}/group/five`,
      six: `${ROOTS.DASHBOARD}/group/six`,
    },
  },
};
