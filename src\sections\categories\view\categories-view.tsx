import { Box, Typography, useTheme, CircularProgress, TextField, InputAdornment, Divider } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { AppContainer } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import { useTranslation } from 'react-i18next';
import { AppTable, useTable, AppTablePropsType } from 'src/components/table';
import { fDate } from 'src/utils/format-time';
import LongMenu from 'src/components/long-menu';
import { Category } from 'src/services/api/use-categories-api';
import CategoryDialog from '../form/category-dialog';
import { useCategoriesView } from '../hooks/use-categories-view';
import { useCategoriesSearch } from '../hooks/use-categories-search';

// ----------------------------------------------------------------------

const ViewAllCategories = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Use search hook for categories with API integration
  const {
    searchQuery,
    categoriesResponse,
    isLoading: searchLoading,
    isError,
    handleSearchChange,
    clearSearch,
  } = useCategoriesSearch();

  // Use categories view hook for CRUD operations only
  const {
    openDialog,
    selectedCategory,
    isLoading: crudLoading,
    handleCloseDialog,
    handleUpdateCategory,
    handleDeleteCategory,
    handleEditCategory,
  } = useCategoriesView();

  // Get categories from search response
  const categories = categoriesResponse?.categories || [];
  const loading = searchLoading;
  const error = isError ? 'Failed to fetch categories' : null;

  // Table hook
  const table = useTable();

  // Handle form submission for editing only (creating is now on separate page)
  const handleFormSubmit = (data: any) => {
    if (selectedCategory) {
      handleUpdateCategory(data);
    }
  };

  // Handle navigation to create category page
  const handleNavigateToCreate = () => {
    navigate(paths.dashboard.categories.create);
  };

  // Menu options for each category row
  const getMenuOptions = (category: Category) => [
    {
      label: t('categories.table.edit'),
      icon: 'eva:edit-fill',
      onClick: () => handleEditCategory(category?.id),
      color: 'inherit',
    },
    {
      label: t('categories.table.delete'),
      icon: 'eva:trash-2-outline',
      onClick: () => handleDeleteCategory(category?.id),
      color: 'error.main',
    },
  ];

  // Table columns configuration
  const columns: AppTablePropsType<Category>['columns'] = [
    {
      name: 'icon',
      PreviewComponent: (category: Category) => (
        <Box
          sx={{
            width: 40,
            height: 40,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: category?.colorType === 'custom'
              ? category?.customColor
              : category?.colorType
                ? theme.palette[category?.colorType]?.main
                : category?.theme,
          }}
        >
          <Iconify icon={category?.icon} width={32} height={32} />
        </Box>
      ),
    },
    {
      name: 'name',
      PreviewComponent: (category: Category) => (
        <Typography variant="subtitle2" noWrap>
          {category?.name}
        </Typography>
      ),
    },
    {
      name: 'createdAt',
      PreviewComponent: (category: Category) => (
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {fDate(category?.createdAt)}
        </Typography>
      ),
    },
    {
      name: 'agentsCount',
      PreviewComponent: (category: Category) => (
        <Typography variant="body2">
          {category?.agentsCount || 0} {t('components.navigation.agents')}
        </Typography>
      ),
    },
    {
      name: 'id',
      cellSx: { width: '50px' },
      PreviewComponent: (category: Category) => (
        <LongMenu options={getMenuOptions(category)} />
      ),
    },
  ];

  return (
    <AppContainer
      title={t('categories.title')}
      pageTitle={t('categories.title')}
    
      buttons={[
        {
          label: t('categories.createNew'),
          variant: 'contained',
          startIcon: <Iconify icon="majesticons:plus" />,
          onClick: handleNavigateToCreate,
        },
      ]}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            {error}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {t('categories.tryAgain')}
          </Typography>
        </Box>
      ) : (
        <>
        <Divider/>
          {/* Search Input */}
          <Box sx={{mt:5}} >
            <TextField
              fullWidth
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder={t('categories.search')}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                  </InputAdornment>
                ),
                ...(searchQuery && {
                  endAdornment: (
                    <InputAdornment position="end">
                      <Box
                        component="button"
                        onClick={clearSearch}
                        sx={{
                          border: 'none',
                          background: 'none',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          p: 0,
                        }}
                      >
                        <Iconify icon="eva:close-fill" sx={{ color: 'text.disabled' }} />
                      </Box>
                    </InputAdornment>
                  ),
                }),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  bgcolor: 'divider',
                },
              }}
            />
          </Box>

          <AppTable<Category>
          headLabels={[
            { id: 'icon', label: '', width: '60px' },
            { id: 'name', label: t('categories.table.name') },
            { id: 'createdAt', label: t('categories.table.dateCreated') },
            { id: 'agentsCount', label: t('categories.table.agentsCount') },
            { id: 'actions', label: t('categories.table.action'), width: '60px' },
          ]}
          dataCount={categories.length}
          data={categories}
          columns={columns}
          table={table}
          isLoading={loading}
          noDataLabel={t('categories.noCategories')}
          sx={{
            mt: 3,
            '& .MuiTableCell-root': {
              borderBottom: '1px solid',
              borderColor: 'divider',
            },
          }}
        />
        </>
      )}

      {/* Category Dialog */}
      <CategoryDialog
        open={openDialog}
        onClose={handleCloseDialog}
        onSubmit={handleFormSubmit}
        defaultValues={selectedCategory || undefined}
        loading={crudLoading}
      />
    </AppContainer>
  );
};

export default ViewAllCategories;
