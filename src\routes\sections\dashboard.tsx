import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { DashboardLayout } from 'src/layouts/dashboard';
import { ProfileLayout } from 'src/layouts/profile/layout';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';
import { paths } from '../paths';

// ----------------------------------------------------------------------

const IndexPage = lazy(() => import('src/pages/dashboard/one'));
const CategoriesPage = lazy(() => import('src/pages/dashboard/categories/categories-page'));
const CreateCategoryPage = lazy(() => import('src/pages/dashboard/categories/create-category-page'));
const AgentsPage = lazy(() => import('src/pages/dashboard/agents/agents-page'));
const CreateAgentPage = lazy(() => import('src/pages/dashboard/agents/create-agent-page'));
const TeamsPage = lazy(() => import('src/pages/dashboard/teams/teams-page'));
const ManagersPage = lazy(() => import('src/pages/dashboard/managers/managers-page'));
const CreateManagerPage = lazy(() => import('src/pages/dashboard/managers/create-manager-page'));
const EditManagerPage = lazy(() => import('src/pages/dashboard/managers/edit-manager-page'));
const ProfilePage = lazy(() => import('src/pages/dashboard/profile/profile-page'));
const KnowledgeBasePage = lazy(() => import('src/pages/dashboard/profile/knowledge-base-page'));
const PreferencesPage = lazy(() => import('src/pages/dashboard/profile/preferences-page'));
const SettingsPage = lazy(() => import('src/pages/dashboard/profile/settings-page'));

// const PageTwo = lazy(() => import('src/pages/dashboard/two'));

// ----------------------------------------------------------------------

const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      { element: <IndexPage />, index: true },
      { path: paths.dashboard.categories.root, element: <CategoriesPage /> },
      { path: paths.dashboard.categories.create, element: <CreateCategoryPage /> },
      { path: paths.dashboard.agents.root, element: <AgentsPage /> },
      { path: paths.dashboard.agents.create, element: <CreateAgentPage /> },
      { path: paths.dashboard.agents.edit, element: <CreateAgentPage /> },
      { path: 'teams', element: <TeamsPage /> },
      { path: paths.dashboard.managers.root, element: <ManagersPage /> },
      { path: paths.dashboard.managers.create, element: <CreateManagerPage /> },
      { path: paths.dashboard.managers.edit, element: <EditManagerPage /> },

      // { path: 'two', element: <PageTwo /> },
      // { path: 'three', element: <PageThree /> },
      // {
      //   path: 'group',
      //   children: [
      //     { element: <PageFour />, index: true },
      //     { path: 'five', element: <PageFive /> },
      //     { path: 'six', element: <PageSix /> },
      //   ],
      // },
    ],
  },
  {
    path: 'dashboard/profile',
    element: CONFIG.auth.skip ? (
      <ProfileLayout>
        <Suspense fallback={<LoadingScreen />}>
          <Outlet />
        </Suspense>
      </ProfileLayout>
    ) : (
      <AuthGuard>
        <ProfileLayout>
          <Suspense fallback={<LoadingScreen />}>
            <Outlet />
          </Suspense>
        </ProfileLayout>
      </AuthGuard>
    ),
    children: [
      { element: <ProfilePage />, index: true },
      { path: 'knowledge-base', element: <KnowledgeBasePage /> },
      { path: 'preferences', element: <PreferencesPage /> },
      { path: 'settings', element: <SettingsPage /> },
    ],
  },
];
